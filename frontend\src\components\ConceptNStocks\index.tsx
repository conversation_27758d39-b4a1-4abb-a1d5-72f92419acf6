import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import {
  Card,
  Table,
  Typography,
  Spin,
  Alert,
  Space,
  Tag,
  Button,
  Row,
  Col,
  Statistic,
  Breadcrumb,
  Tooltip,
  App
} from 'antd'
import {
  ReloadOutlined,
  ArrowLeftOutlined,
  HomeOutlined,
  BulbOutlined,
  ExperimentOutlined,
  CalendarOutlined,
  DollarOutlined,
  LineChartOutlined,
  InfoCircleOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons'
import { apiService } from '../../services/api'
import { useTheme } from '../../theme/ThemeProvider'
import type { ColumnsType } from 'antd/es/table'

const { Title, Text } = Typography

// N型待选股票数据接口
interface ConceptNStockData {
  stock_code: string
  stock_name: string
  limit_up_date: string
  decline_days: number
  continuous_decline: string
  full_stock_name: string
  theme: string
  theme_summary: string
  theme_g: string
  trade_date: string
  ths_concept_names: string
  sector_name: string
  em_sector_names: string
  created_at: string
  updated_at: string
  large_buy_data: {
    exists: boolean
    总买入金额: number
    数量: number
    总卖出金额: number
    买卖比: number
    总买入占比: number
  }
  // 新增实时数据字段
  涨跌幅?: number | null
  最新价?: number | null
  涨跌额?: number | null
  更新时间?: string
  昨收价?: number | null
}

const ConceptNStocks: React.FC = () => {
  const { conceptName } = useParams<{ conceptName: string }>()
  const navigate = useNavigate()
  const { colorScheme } = useTheme()
  const { message } = App.useApp()
  const [loading, setLoading] = useState(true)
  const [data, setData] = useState<ConceptNStockData[]>([])
  const [error, setError] = useState<string | null>(null)
  const [lastUpdateTime, setLastUpdateTime] = useState<string>('')

  // 获取涨跌幅颜色
  const getChangeColor = (change: number | null | undefined) => {
    if (change === null || change === undefined) return colorScheme.onSurface
    if (change > 0) return '#f5222d'  // 红色
    if (change < 0) return '#52c41a'  // 绿色
    return colorScheme.onSurface      // 灰色
  }

  // 加载概念N型待选股票数据
  const loadConceptNStocks = async () => {
    if (!conceptName) {
      setError('概念名称参数缺失')
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)
      
      const response = await apiService.getConceptNStocks(conceptName)
      
      if (response.data.success) {
        setData(response.data.data)

        // 设置数据更新时间
        if (response.data.timestamp) {
          const updateTime = new Date(response.data.timestamp).toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
          })
          setLastUpdateTime(updateTime)
        }

        message.success(`成功加载${response.data.total}只${conceptName}概念N型待选股票`)
      } else {
        setError(response.data.error || '获取N型待选股票数据失败')
      }
    } catch (err: any) {
      console.error('获取N型待选股票数据失败:', err)
      setError(err.response?.data?.error || err.message || '获取N型待选股票数据失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    // 设置页面标题
    if (conceptName) {
      document.title = `${conceptName}N型待选股票 - 股票分析系统`
    }
    
    loadConceptNStocks()
    
    // 组件卸载时恢复默认标题
    return () => {
      document.title = '股票分析系统'
    }
  }, [conceptName])



  // 渲染缩量下跌天数标签
  const renderDeclineDaysTag = (days: number) => {
    if (days >= 5) {
      return <Tag color="red">{days}天</Tag>
    } else if (days >= 3) {
      return <Tag color="orange">{days}天</Tag>
    } else if (days >= 1) {
      return <Tag color="blue">{days}天</Tag>
    } else {
      return <Tag color="default">0天</Tag>
    }
  }

  // 表格列定义
  const columns: ColumnsType<ConceptNStockData> = [
    {
      title: '排名',
      key: 'rank',
      width: 60,
      render: (_, __, index) => (
        <Text strong style={{ color: colorScheme.primary }}>
          {index + 1}
        </Text>
      ),
    },
    {
      title: '股票代码',
      dataIndex: 'stock_code',
      key: 'stock_code',
      width: 100,
      sorter: (a, b) => a.stock_code.localeCompare(b.stock_code),
      render: (code: string) => (
        <Text style={{ fontFamily: 'monospace', color: colorScheme.onSurface }}>
          {code}
        </Text>
      ),
    },
    {
      title: '股票名称',
      dataIndex: 'stock_name',
      key: 'stock_name',
      width: 100,
      sorter: (a, b) => a.stock_name.localeCompare(b.stock_name),
      render: (name: string) => (
        <Text strong style={{ color: colorScheme.onSurface }}>
          {name}
        </Text>
      ),
    },
    {
      title: (
        <Tooltip title="实时涨跌幅，红涨绿跌">
          <span>涨跌幅 <QuestionCircleOutlined style={{ fontSize: '12px', opacity: 0.6 }} /></span>
        </Tooltip>
      ),
      dataIndex: '涨跌幅',
      key: '涨跌幅',
      width: 80,
      align: 'right',
      sorter: (a, b) => (a.涨跌幅 || 0) - (b.涨跌幅 || 0),
      render: (change: number | null) => {
        if (change === null || change === undefined) {
          return (
            <Text style={{ color: colorScheme.onSurface, fontSize: '12px' }}>
              -
            </Text>
          )
        }
        return (
          <div style={{
            padding: '2px 6px',
            borderRadius: '4px',
            backgroundColor: `${getChangeColor(change)}15`,
            border: `1px solid ${getChangeColor(change)}30`
          }}>
            <Text style={{
              color: getChangeColor(change),
              fontFamily: 'monospace',
              fontWeight: 600,
              fontSize: '12px'
            }}>
              {change > 0 ? '+' : ''}{change.toFixed(2)}%
            </Text>
          </div>
        )
      },
    },
    {
      title: (
        <Tooltip title="实时最新价格">
          <span>最新价 <QuestionCircleOutlined style={{ fontSize: '12px', opacity: 0.6 }} /></span>
        </Tooltip>
      ),
      dataIndex: '最新价',
      key: '最新价',
      width: 80,
      align: 'right',
      sorter: (a, b) => (a.最新价 || 0) - (b.最新价 || 0),
      render: (price: number | null) => {
        if (price === null || price === undefined) {
          return (
            <Text style={{ color: colorScheme.onSurface, fontSize: '12px' }}>
              -
            </Text>
          )
        }
        return (
          <Text style={{
            color: colorScheme.onSurface,
            fontFamily: 'monospace',
            fontSize: '12px',
            fontWeight: 500
          }}>
            ¥{price.toFixed(2)}
          </Text>
        )
      },
    },
    {
      title: '题材汇总',
      dataIndex: 'theme_summary',
      key: 'theme_summary',
      width: 200,
      render: (summary: string) => (
        <Text
          style={{
            color: colorScheme.primary,
            fontSize: '12px',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden'
          }}
          title={summary}
        >
          {summary || '未知'}
        </Text>
      ),
    },
    {
      title: '涨停日期',
      dataIndex: 'limit_up_date',
      key: 'limit_up_date',
      width: 90,
      sorter: (a, b) => a.limit_up_date.localeCompare(b.limit_up_date),
      render: (date: string) => (
        <Text style={{ color: colorScheme.onSurface, fontSize: '12px' }}>
          {date}
        </Text>
      ),
    },
    {
      title: '缩量天数',
      dataIndex: 'decline_days',
      key: 'decline_days',
      width: 80,
      sorter: (a, b) => a.decline_days - b.decline_days,
      render: (days: number) => renderDeclineDaysTag(days),
    },
    {
      title: (
        <Tooltip title="显示股票在大笔买入表中的状态">
          <span>大笔买入 <InfoCircleOutlined style={{ fontSize: '12px', opacity: 0.6 }} /></span>
        </Tooltip>
      ),
      key: 'large_buy_status',
      width: 140,
      align: 'center',
      render: (_, record: ConceptNStockData) => {
        const largeBuyData = record.large_buy_data
        if (!largeBuyData?.exists) {
          return <Text style={{ color: colorScheme.onSurfaceVariant }}>-</Text>
        }

        const buyAmount = largeBuyData.总买入金额 || 0
        const sellAmount = largeBuyData.总卖出金额 || 0
        const buyRatio = largeBuyData.买卖比 || 0
        const buyPercent = (largeBuyData.总买入占比 || 0) * 100

        return (
          <div style={{ fontSize: '11px', lineHeight: '1.3' }}>
            <div style={{ color: colorScheme.onSurface }}>
              <Text strong>买入: </Text>
              <Text style={{ color: colorScheme.error }}>{buyAmount.toFixed(2)}万</Text>
              <Text style={{ marginLeft: '8px' }}>数量: {largeBuyData.数量 || 0}</Text>
            </div>
            <div style={{ color: colorScheme.onSurface, marginTop: '2px' }}>
              <Text strong>卖出: </Text>
              <Text style={{ color: colorScheme.success }}>{sellAmount.toFixed(2)}万</Text>
              <Text style={{ marginLeft: '8px' }}>比: {buyRatio.toFixed(2)}</Text>
            </div>
            <div style={{ color: colorScheme.onSurface, marginTop: '2px' }}>
              <Text strong>占比: </Text>
              <Text style={{ color: colorScheme.primary }}>{buyPercent.toFixed(3)}%</Text>
            </div>
          </div>
        )
      },
    },
  ]

  // 计算统计数据
  const totalStocks = data.length
  const maxDeclineDays = data.length > 0 ? Math.max(...data.map(item => item.decline_days)) : 0

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '400px',
        backgroundColor: colorScheme.background
      }}>
        <Spin size="large" />
      </div>
    )
  }

  if (error) {
    return (
      <div style={{ padding: '24px', backgroundColor: colorScheme.background }}>
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <Button size="small" onClick={loadConceptNStocks}>
              重试
            </Button>
          }
        />
      </div>
    )
  }

  return (
    <div style={{ backgroundColor: colorScheme.background, minHeight: '100vh' }}>
      {/* 面包屑导航 */}
      <Card 
        size="small" 
        style={{ 
          marginBottom: '16px',
          backgroundColor: colorScheme.surfaceContainerLow,
          borderColor: colorScheme.outlineVariant
        }}
      >
        <Space>
          <Breadcrumb
            items={[
              {
                href: '/',
                title: <HomeOutlined />,
              },
              {
                href: '/concept-analysis',
                title: (
                  <Space>
                    <BulbOutlined />
                    <span>概念分析</span>
                  </Space>
                ),
              },
              {
                title: `${conceptName}N型待选股票`,
              },
            ]}
          />
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate(-1)}
            style={{ color: colorScheme.primary }}
          >
            返回
          </Button>
        </Space>
      </Card>

      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <Title 
          level={2} 
          style={{ 
            color: colorScheme.onSurface, 
            margin: 0,
            display: 'flex',
            alignItems: 'center',
            gap: '12px'
          }}
        >
          <ExperimentOutlined style={{ color: colorScheme.primary }} />
          {conceptName}N型待选股票
        </Title>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
          <Text type="secondary" style={{ color: colorScheme.onSurfaceVariant }}>
            {conceptName}概念板块的N型待选股票详细信息
          </Text>
          {lastUpdateTime && (
            <Text
              type="secondary"
              style={{
                color: colorScheme.primary,
                fontSize: '12px',
                fontWeight: 500
              }}
            >
              数据更新时间: {lastUpdateTime}
            </Text>
          )}
        </div>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={12}>
          <Card
            size="small"
            style={{
              backgroundColor: colorScheme.surfaceContainerLow,
              borderColor: colorScheme.outlineVariant
            }}
          >
            <Statistic
              title="待选股票数"
              value={totalStocks}
              valueStyle={{ color: colorScheme.primary }}
              prefix={<ExperimentOutlined />}
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card
            size="small"
            style={{
              backgroundColor: colorScheme.surfaceContainerLow,
              borderColor: colorScheme.outlineVariant
            }}
          >
            <Statistic
              title="最大缩量天数"
              value={maxDeclineDays}
              suffix="天"
              valueStyle={{ color: '#722ed1' }}
              prefix={<CalendarOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 数据表格 */}
      <Card
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
            <Space>
              <Text strong style={{ color: colorScheme.onSurface }}>
                N型待选股票列表
              </Text>
              {lastUpdateTime && (
                <Text
                  type="secondary"
                  style={{
                    color: colorScheme.onSurfaceVariant,
                    fontSize: '12px'
                  }}
                >
                  (更新时间: {lastUpdateTime})
                </Text>
              )}
            </Space>
            <Button
              type="text"
              icon={<ReloadOutlined />}
              onClick={loadConceptNStocks}
              loading={loading}
              style={{ color: colorScheme.primary }}
            >
              刷新
            </Button>
          </div>
        }
        style={{
          backgroundColor: colorScheme.surface,
          borderColor: colorScheme.outlineVariant
        }}
      >
        <Table
          columns={columns}
          dataSource={data}
          rowKey="stock_code"
          pagination={false}
          size="small"
          style={{
            backgroundColor: colorScheme.surface
          }}
        />
      </Card>
    </div>
  )
}

export default ConceptNStocks

import React, { useState, useEffect, useCallback } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import {
  Card,
  Spin,
  Alert,
  Button,
  Table,
  Row,
  Col,
  Typography,
  Tag,
  Breadcrumb,
  Space,
  Statistic,
  Empty,
  Tooltip,
  Progress,
  Result,
  App
} from 'antd'
import {
  ArrowLeftOutlined,
  HomeOutlined,
  AppstoreOutlined,
  RiseOutlined,
  FallOutlined,
  ReloadOutlined,
  CaretUpOutlined,
  CaretDownOutlined,
  InfoCircleOutlined,
  QuestionCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { apiService } from '../../services/api'
import { useTheme } from '../../theme/ThemeProvider'

const { Title, Text } = Typography

// 特殊数据表状态接口
interface NShapeStatus {
  exists: boolean
  涨停日期?: string
  缩量下跌天数?: number
  持续缩量?: string
  最后一天收盘价?: number
}

interface LargeBuyStatus {
  exists: boolean
  总买入金额?: number
  数量?: number
  总卖出金额?: number
  买卖比?: number
  总买入占比?: number
}

interface SpecialDataStatus {
  n_shape_status: NShapeStatus
  large_buy_status: LargeBuyStatus
}

// 股票数据接口定义
interface StockData {
  序号: number
  代码: string
  名称: string
  最新价: number
  涨跌幅: number
  涨跌额: number
  成交量: number
  成交额: number
  振幅: number
  最高: number
  最低: number
  今开: number
  昨收: number
  量比: number
  换手率: number
  市盈率动态: number
  市净率: number
  // 新增特殊数据字段
  specialData?: SpecialDataStatus
}

// 板块信息接口
interface SectorInfo {
  sector_code: string
  sector_name: string
  total_stocks: number
  up_stocks: number
  down_stocks: number
  flat_stocks: number
}

const SectorStocksList: React.FC = () => {
  const { sectorCode } = useParams<{ sectorCode: string }>()
  const navigate = useNavigate()
  const { colorScheme } = useTheme()
  const { message } = App.useApp()

  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [stocks, setStocks] = useState<StockData[]>([])
  const [sectorInfo, setSectorInfo] = useState<SectorInfo | null>(null)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [specialDataLoading, setSpecialDataLoading] = useState(false)
  const [dataUpdateTime, setDataUpdateTime] = useState<string>('')

  // 获取状态显示文本
  const getStatusText = () => {
    return '全部股票'
  }

  // 获取状态图标
  const getStatusIcon = () => {
    return <AppstoreOutlined />
  }

  // 获取涨跌幅颜色
  const getChangeColor = (change: number) => {
    if (change > 0) return colorScheme.error // 红色表示上涨
    if (change < 0) return colorScheme.success // 绿色表示下跌
    return colorScheme.onSurface // 平盘
  }

  // 加载特殊数据表信息
  const loadSpecialData = useCallback(async (stockList: StockData[]) => {
    if (!stockList.length) return stockList

    try {
      setSpecialDataLoading(true)

      // 提取股票代码
      const stockCodes = stockList.map(stock => stock.代码)

      const response = await apiService.getStocksSpecialData(stockCodes)

      if (response.data.success) {
        const specialDataMap = response.data.data

        // 将特殊数据和实时数据合并到股票列表中
        const enrichedStocks = stockList.map(stock => {
          const specialData = specialDataMap[stock.代码] || {
            n_shape_status: { exists: false },
            large_buy_status: { exists: false },
            realtime_data: {}
          }

          // 如果有实时数据，更新股票的涨跌幅信息
          const realtimeData = specialData.realtime_data || {}
          const updatedStock = { ...stock }

          // 使用实时数据更新涨跌幅相关字段（如果可用）
          if (realtimeData.涨跌幅 !== null && realtimeData.涨跌幅 !== undefined) {
            updatedStock.涨跌幅 = realtimeData.涨跌幅
          }
          if (realtimeData.最新价 !== null && realtimeData.最新价 !== undefined) {
            updatedStock.最新价 = realtimeData.最新价
          }
          if (realtimeData.涨跌额 !== null && realtimeData.涨跌额 !== undefined) {
            updatedStock.涨跌额 = realtimeData.涨跌额
          }
          if (realtimeData.股票名称) {
            updatedStock.名称 = realtimeData.股票名称
          }

          return {
            ...updatedStock,
            specialData: {
              n_shape_status: specialData.n_shape_status,
              large_buy_status: specialData.large_buy_status
            },
            realtimeData: realtimeData
          }
        })

        return enrichedStocks
      } else {
        console.warn('获取特殊数据失败:', response.data.error)
        return stockList
      }
    } catch (err: any) {
      console.error('获取特殊数据失败:', err)
      return stockList
    } finally {
      setSpecialDataLoading(false)
    }
  }, [])

  // 加载板块股票数据
  const loadSectorStocks = useCallback(async () => {
    if (!sectorCode) {
      setError('板块代码参数缺失')
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const response = await apiService.getSectorStocks(sectorCode)

      if (response.data.success) {
        const data = response.data.data
        const basicStocks = data.stocks || []
        setSectorInfo(data.sector_info || null)

        // 设置数据更新时间
        if (response.data.timestamp) {
          try {
            const timestamp = new Date(response.data.timestamp)
            setDataUpdateTime(timestamp.toLocaleString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
              hour12: false
            }))
          } catch (e) {
            console.warn('时间戳格式转换失败:', response.data.timestamp)
          }
        }

        // 加载特殊数据并合并
        const enrichedStocks = await loadSpecialData(basicStocks)
        setStocks(enrichedStocks)

        message.success(`成功加载${data.sector_info?.sector_name || '板块'}的${getStatusText()}数据`)
      } else {
        setError(response.data.error || '获取股票列表失败')
      }
    } catch (err: any) {
      console.error('获取股票列表失败:', err)
      setError(err.response?.data?.error || err.message || '获取股票列表失败')
    } finally {
      setLoading(false)
    }
  }, [sectorCode, loadSpecialData])

  // 刷新数据
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true)
    await loadSectorStocks()
    setIsRefreshing(false)
  }, [loadSectorStocks])

  useEffect(() => {
    loadSectorStocks()
  }, [loadSectorStocks])

  // 设置页面标题
  useEffect(() => {
    if (sectorInfo?.sector_name) {
      const statusText = getStatusText()
      document.title = `${statusText} - ${sectorInfo.sector_name} | 股票分析系统`
    } else {
      document.title = '股票列表 | 股票分析系统'
    }

    // 组件卸载时恢复默认标题
    return () => {
      document.title = '股票分析系统'
    }
  }, [sectorInfo?.sector_name])

  // 键盘快捷键支持
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        navigate('/')
      } else if (event.key === 'F5' || (event.ctrlKey && event.key === 'r')) {
        event.preventDefault()
        handleRefresh()
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => {
      window.removeEventListener('keydown', handleKeyPress)
    }
  }, [navigate, handleRefresh])

  // 表格列配置
  const columns: ColumnsType<StockData> = [
    {
      title: '序号',
      dataIndex: '序号',
      key: '序号',
      width: 50,
      align: 'center',
      render: (value: number) => (
        <Text style={{ color: colorScheme.onSurface, fontFamily: 'monospace' }}>
          {value}
        </Text>
      ),
    },
    {
      title: (
        <Tooltip title="点击可复制股票代码">
          <span>股票代码 <QuestionCircleOutlined style={{ fontSize: '12px', opacity: 0.6 }} /></span>
        </Tooltip>
      ),
      dataIndex: '代码',
      key: '代码',
      width: 70,
      render: (code: string) => (
        <Tooltip title="点击复制代码">
          <Text
            style={{
              color: colorScheme.primary,
              fontFamily: 'monospace',
              fontWeight: 600,
              cursor: 'pointer'
            }}
            onClick={() => {
              navigator.clipboard.writeText(code)
              message.success(`已复制股票代码: ${code}`)
            }}
          >
            {code}
          </Text>
        </Tooltip>
      ),
    },
    {
      title: '股票名称',
      dataIndex: '名称',
      key: '名称',
      width: 80,
      render: (name: string) => (
        <Text style={{ color: colorScheme.onSurface, fontWeight: 500 }}>
          {name}
        </Text>
      ),
    },
    {
      title: '最新价',
      dataIndex: '最新价',
      key: '最新价',
      width: 70,
      align: 'right',
      sorter: (a, b) => a.最新价 - b.最新价,
      render: (price: number) => (
        <Text style={{ 
          color: colorScheme.onSurface, 
          fontFamily: 'monospace',
          fontWeight: 600
        }}>
          ¥{price.toFixed(2)}
        </Text>
      ),
    },
    {
      title: (
        <Tooltip title="按涨跌幅排序，红涨绿跌">
          <span>涨跌幅 <QuestionCircleOutlined style={{ fontSize: '12px', opacity: 0.6 }} /></span>
        </Tooltip>
      ),
      dataIndex: '涨跌幅',
      key: '涨跌幅',
      width: 80,
      align: 'right',
      sorter: (a, b) => a.涨跌幅 - b.涨跌幅,
      render: (change: number) => (
        <div style={{
          padding: '2px 6px',
          borderRadius: '4px',
          backgroundColor: `${getChangeColor(change)}15`,
          border: `1px solid ${getChangeColor(change)}30`
        }}>
          <Text style={{
            color: getChangeColor(change),
            fontFamily: 'monospace',
            fontWeight: 600,
            fontSize: '13px'
          }}>
            {change > 0 ? '+' : ''}{change.toFixed(2)}%
          </Text>
        </div>
      ),
    },
    {
      title: '成交量',
      dataIndex: '成交量',
      key: '成交量',
      width: 80,
      align: 'right',
      sorter: (a, b) => a.成交量 - b.成交量,
      render: (volume: number) => (
        <Text style={{ color: colorScheme.onSurface, fontFamily: 'monospace' }}>
          {volume >= 10000 ? `${(volume / 10000).toFixed(1)}万` : volume.toLocaleString()}
        </Text>
      ),
    },
    {
      title: '成交额',
      dataIndex: '成交额',
      key: '成交额',
      width: 80,
      align: 'right',
      sorter: (a, b) => a.成交额 - b.成交额,
      render: (amount: number) => (
        <Text style={{ color: colorScheme.onSurface, fontFamily: 'monospace' }}>
          {amount >= 100000000 ? `${(amount / 100000000).toFixed(2)}亿` : 
           amount >= 10000 ? `${(amount / 10000).toFixed(1)}万` : 
           amount.toLocaleString()}
        </Text>
      ),
    },
    {
      title: '换手率',
      dataIndex: '换手率',
      key: '换手率',
      width: 70,
      align: 'right',
      sorter: (a, b) => a.换手率 - b.换手率,
      render: (rate: number) => (
        <Text style={{ color: colorScheme.onSurface, fontFamily: 'monospace' }}>
          {rate.toFixed(2)}%
        </Text>
      ),
    },
    {
      title: (
        <Tooltip title="显示股票在N形待选表中的状态">
          <span>N形待选 <InfoCircleOutlined style={{ fontSize: '12px', opacity: 0.6 }} /></span>
        </Tooltip>
      ),
      key: 'n_shape_status',
      width: 100,
      align: 'center',
      render: (_, record: StockData) => {
        if (specialDataLoading) {
          return <Spin size="small" />
        }

        const nShapeStatus = record.specialData?.n_shape_status
        if (!nShapeStatus?.exists) {
          return <Text style={{ color: colorScheme.onSurfaceVariant }}>-</Text>
        }

        return (
          <div style={{ fontSize: '12px', lineHeight: '1.4' }}>
            <div style={{ color: colorScheme.onSurface }}>
              <Text strong>涨停: </Text>
              <Text style={{ color: colorScheme.primary }}>{nShapeStatus.涨停日期}</Text>
            </div>
            <div style={{ color: colorScheme.onSurface, marginTop: '2px' }}>
              <Text strong>缩量: </Text>
              <Text style={{ color: colorScheme.secondary }}>{nShapeStatus.缩量下跌天数}天</Text>
            </div>
          </div>
        )
      },
    },
    {
      title: (
        <Tooltip title="显示股票在大笔买入表中的状态">
          <span>大笔买入 <InfoCircleOutlined style={{ fontSize: '12px', opacity: 0.6 }} /></span>
        </Tooltip>
      ),
      key: 'large_buy_status',
      width: 120,
      align: 'center',
      render: (_, record: StockData) => {
        if (specialDataLoading) {
          return <Spin size="small" />
        }

        const largeBuyStatus = record.specialData?.large_buy_status
        if (!largeBuyStatus?.exists) {
          return <Text style={{ color: colorScheme.onSurfaceVariant }}>-</Text>
        }

        const buyAmount = largeBuyStatus.总买入金额 || 0
        const sellAmount = largeBuyStatus.总卖出金额 || 0
        const buyRatio = largeBuyStatus.买卖比 || 0
        const buyPercent = (largeBuyStatus.总买入占比 || 0) * 100

        return (
          <div style={{ fontSize: '11px', lineHeight: '1.3' }}>
            <div style={{ color: colorScheme.onSurface }}>
              <Text strong>买入: </Text>
              <Text style={{ color: colorScheme.error }}>{buyAmount.toFixed(2)}万</Text>
              <Text style={{ marginLeft: '8px' }}>数量: {largeBuyStatus.数量 || 0}</Text>
            </div>
            <div style={{ color: colorScheme.onSurface, marginTop: '2px' }}>
              <Text strong>卖出: </Text>
              <Text style={{ color: colorScheme.success }}>{sellAmount.toFixed(2)}万</Text>
              <Text style={{ marginLeft: '8px' }}>比: {buyRatio.toFixed(2)}</Text>
            </div>
            <div style={{ color: colorScheme.onSurface, marginTop: '2px' }}>
              <Text strong>占比: </Text>
              <Text style={{ color: colorScheme.primary }}>{buyPercent.toFixed(3)}%</Text>
            </div>
          </div>
        )
      },
    },
  ]

  if (loading) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: colorScheme.background,
        padding: '50px'
      }}>
        <Card
          style={{
            backgroundColor: colorScheme.surfaceContainerLow,
            borderColor: colorScheme.outlineVariant,
            textAlign: 'center',
            minWidth: '400px'
          }}
        >
          <Space direction="vertical" size="large">
            <Spin size="large" />
            <div>
              <Title level={3} style={{ color: colorScheme.onSurface, margin: 0 }}>
                加载股票数据中...
              </Title>
              <Text style={{ color: colorScheme.onSurfaceVariant }}>
                正在获取{sectorCode ? `板块 ${sectorCode}` : ''}的{getStatusText()}，请稍候
              </Text>
            </div>
            <Progress
              percent={50}
              status="active"
              showInfo={false}
              strokeColor={colorScheme.primary}
            />
            <Text style={{ color: colorScheme.onSurfaceVariant, fontSize: '12px' }}>
              <InfoCircleOutlined /> 首次加载可能需要较长时间，后续访问将使用缓存数据
            </Text>
          </Space>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: colorScheme.background,
        padding: '50px'
      }}>
        <Result
          status="error"
          title="数据加载失败"
          subTitle={error}
          extra={[
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              key="retry"
              style={{
                backgroundColor: colorScheme.primary,
                borderColor: colorScheme.primary
              }}
            >
              重新加载
            </Button>,
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => navigate('/')}
              key="back"
              style={{
                borderColor: colorScheme.outline,
                color: colorScheme.onSurface
              }}
            >
              返回主页
            </Button>
          ]}
          style={{
            backgroundColor: colorScheme.surfaceContainerLow,
            borderRadius: '12px',
            padding: '40px'
          }}
        />
      </div>
    )
  }

  return (
    <div style={{ padding: '24px', backgroundColor: colorScheme.background }}>
      {/* 面包屑导航 */}
      <Card
        size="small" 
        style={{ 
          marginBottom: 16,
          backgroundColor: colorScheme.surfaceContainerLow,
          borderColor: colorScheme.outlineVariant
        }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Breadcrumb
            items={[
              {
                href: '#',
                title: <HomeOutlined />,
                onClick: (e) => {
                  e.preventDefault()
                  navigate('/')
                }
              },
              {
                href: '#',
                title: (
                  <>
                    <AppstoreOutlined />
                    <span>全部86板块</span>
                  </>
                ),
                onClick: (e) => {
                  e.preventDefault()
                  navigate('/')
                }
              },
              {
                href: '#',
                title: sectorInfo?.sector_name || '板块详情',
                onClick: (e) => {
                  e.preventDefault()
                  navigate(`/sectors/${sectorCode}`)
                }
              },
              {
                title: (
                  <>
                    {getStatusIcon()}
                    <span>{getStatusText()}</span>
                  </>
                ),
              },
            ]}
          />
          <Space>
            <Tooltip title="重新获取最新股票数据">
              <Button
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                loading={isRefreshing}
                size="small"
                style={{
                  borderColor: colorScheme.outline,
                  color: colorScheme.primary
                }}
              >
                刷新数据
              </Button>
            </Tooltip>
            <Tooltip title="返回板块列表页面 (快捷键: Esc)">
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={() => navigate('/')}
                className="md3-button-outlined"
                style={{
                  borderColor: colorScheme.outline,
                  color: colorScheme.primary
                }}
              >
                返回列表
              </Button>
            </Tooltip>
          </Space>
        </div>
      </Card>

      {/* 板块基本信息 */}
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <Title level={3} style={{ margin: 0, color: colorScheme.onSurface }}>
              {sectorInfo?.sector_name}
            </Title>
            <Tag color="blue">{sectorInfo?.sector_code}</Tag>
            {getStatusIcon()}
            <Tag color="default">
              {getStatusText()}
            </Tag>
          </div>
        }
        size="small"
        style={{
          marginBottom: 16,
          backgroundColor: colorScheme.surfaceContainerLow,
          borderColor: colorScheme.outlineVariant
        }}
      >
        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title="总股票数"
              value={sectorInfo?.total_stocks || 0}
              prefix={<AppstoreOutlined style={{ color: colorScheme.primary }} />}
              valueStyle={{
                color: colorScheme.onSurface,
                fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
                fontWeight: 500,
              }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="上涨股票"
              value={sectorInfo?.up_stocks || 0}
              prefix={<RiseOutlined style={{ color: colorScheme.error }} />}
              valueStyle={{
                color: colorScheme.error,
                fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
                fontWeight: 500,
              }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="下跌股票"
              value={sectorInfo?.down_stocks || 0}
              prefix={<FallOutlined style={{ color: colorScheme.success }} />}
              valueStyle={{
                color: colorScheme.success,
                fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
                fontWeight: 500,
              }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="当前显示"
              value={stocks.length}
              prefix={getStatusIcon()}
              valueStyle={{
                color: colorScheme.onSurface,
                fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
                fontWeight: 500,
              }}
            />
          </Col>
        </Row>
      </Card>

      {/* 股票列表表格 */}
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap', gap: '8px' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              {getStatusIcon()}
              <span>{getStatusText()}列表</span>
              <Tag color="blue">{stocks.length}只</Tag>
            </div>
            {dataUpdateTime && (
              <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                <ClockCircleOutlined style={{
                  color: colorScheme.onSurfaceVariant,
                  fontSize: '12px'
                }} />
                <Text style={{
                  color: colorScheme.onSurfaceVariant,
                  fontSize: '12px',
                  fontFamily: 'monospace'
                }}>
                  数据更新时间：{dataUpdateTime}
                </Text>
              </div>
            )}
          </div>
        }
        style={{
          backgroundColor: colorScheme.surfaceContainerLow,
          borderColor: colorScheme.outlineVariant
        }}
      >
        {stocks.length > 0 ? (
          <Table
            columns={columns}
            dataSource={stocks}
            rowKey="代码"
            size="small"
            pagination={false}
            scroll={{ x: 800 }}
            style={{
              backgroundColor: 'transparent',
            }}
            rowClassName={(record) => {
              // 根据涨跌幅添加行样式
              if (record.涨跌幅 > 0) {
                return 'stock-row-up'
              } else if (record.涨跌幅 < 0) {
                return 'stock-row-down'
              }
              return 'stock-row-flat'
            }}
          />
        ) : (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              <span style={{ color: colorScheme.onSurfaceVariant }}>
                暂无{getStatusText()}数据
              </span>
            }
          />
        )}
      </Card>

      {/* 添加自定义样式 */}
      <style>{`
        .stock-row-up {
          background-color: ${colorScheme.errorContainer}08;
        }
        .stock-row-down {
          background-color: ${colorScheme.successContainer}08;
        }
        .stock-row-flat {
          background-color: transparent;
        }
        .stock-row-up:hover {
          background-color: ${colorScheme.errorContainer}15 !important;
        }
        .stock-row-down:hover {
          background-color: ${colorScheme.successContainer}15 !important;
        }
        .stock-row-flat:hover {
          background-color: ${colorScheme.surfaceContainerHighest} !important;
        }
      `}</style>
    </div>
  )
}

export default SectorStocksList

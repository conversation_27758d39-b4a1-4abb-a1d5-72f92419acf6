import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import {
  Card,
  Table,
  Typography,
  Spin,
  Alert,
  Space,
  Tag,
  Button,
  Row,
  Col,
  Statistic,
  Breadcrumb,
  App
} from 'antd'
import {
  ReloadOutlined,
  <PERSON><PERSON>eftOutlined,
  HomeOutlined,
  BulbOutlined,
  TrophyOutlined,
  RiseOutlined,
  ClockCircleOutlined
} from '@ant-design/icons'
import { apiService } from '../../services/api'
import { useTheme } from '../../theme/ThemeProvider'
import type { ColumnsType } from 'antd/es/table'

const { Title, Text } = Typography

// 涨停股票数据接口
interface ConceptStockData {
  stock_code: string
  stock_name: string
  limit_up_price: number
  consecutive_days: number
  increase_pct: number
  first_time: string
  last_time: string
  concept: string
  concept_detail: string
  reason: string
  created_at: string
  updated_at: string
}

const ConceptStocks: React.FC = () => {
  const { conceptName } = useParams<{ conceptName: string }>()
  const navigate = useNavigate()
  const { colorScheme } = useTheme()
  const { message } = App.useApp()
  const [loading, setLoading] = useState(true)
  const [data, setData] = useState<ConceptStockData[]>([])
  const [error, setError] = useState<string | null>(null)

  // 加载概念涨停股票数据
  const loadConceptStocks = async () => {
    if (!conceptName) {
      setError('概念名称参数缺失')
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)
      
      const response = await apiService.getConceptStocks(conceptName)
      
      if (response.data.success) {
        setData(response.data.data)
        message.success(`成功加载${response.data.total}只${conceptName}概念涨停股票`)
      } else {
        setError(response.data.error || '获取涨停股票数据失败')
      }
    } catch (err: any) {
      console.error('获取涨停股票数据失败:', err)
      setError(err.response?.data?.error || err.message || '获取涨停股票数据失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    // 设置页面标题
    if (conceptName) {
      document.title = `${conceptName}涨停股票 - 股票分析系统`
    }
    
    loadConceptStocks()
    
    // 组件卸载时恢复默认标题
    return () => {
      document.title = '股票分析系统'
    }
  }, [conceptName])

  // 渲染连续涨停天数标签
  const renderConsecutiveDaysTag = (days: number) => {
    if (days >= 5) {
      return <Tag color="red">连板{days}天</Tag>
    } else if (days >= 3) {
      return <Tag color="orange">连板{days}天</Tag>
    } else if (days >= 2) {
      return <Tag color="blue">连板{days}天</Tag>
    } else {
      return <Tag color="default">首板</Tag>
    }
  }

  // 渲染涨幅标签
  const renderIncreaseTag = (pct: number) => {
    const color = pct >= 20 ? 'red' : pct >= 10 ? 'orange' : 'green'
    return <Tag color={color}>+{pct.toFixed(2)}%</Tag>
  }

  // 表格列定义
  const columns: ColumnsType<ConceptStockData> = [
    {
      title: '排名',
      key: 'rank',
      width: 80,
      render: (_, __, index) => (
        <Text strong style={{ color: colorScheme.primary }}>
          {index + 1}
        </Text>
      ),
    },
    {
      title: '股票代码',
      dataIndex: 'stock_code',
      key: 'stock_code',
      width: 120,
      sorter: (a, b) => a.stock_code.localeCompare(b.stock_code),
      render: (code: string) => (
        <Text style={{ fontFamily: 'monospace', color: colorScheme.onSurface }}>
          {code}
        </Text>
      ),
    },
    {
      title: '股票名称',
      dataIndex: 'stock_name',
      key: 'stock_name',
      width: 120,
      sorter: (a, b) => a.stock_name.localeCompare(b.stock_name),
      render: (name: string) => (
        <Text strong style={{ color: colorScheme.onSurface }}>
          {name}
        </Text>
      ),
    },
    {
      title: '涨停价',
      dataIndex: 'limit_up_price',
      key: 'limit_up_price',
      width: 100,
      sorter: (a, b) => a.limit_up_price - b.limit_up_price,
      render: (price: number) => (
        <Text style={{ 
          color: '#f5222d',
          fontWeight: 'bold',
          fontFamily: 'monospace'
        }}>
          ¥{price.toFixed(2)}
        </Text>
      ),
    },
    {
      title: '涨幅',
      dataIndex: 'increase_pct',
      key: 'increase_pct',
      width: 100,
      sorter: (a, b) => a.increase_pct - b.increase_pct,
      render: (pct: number) => renderIncreaseTag(pct),
    },
    {
      title: '连板天数',
      dataIndex: 'consecutive_days',
      key: 'consecutive_days',
      width: 100,
      sorter: (a, b) => a.consecutive_days - b.consecutive_days,
      render: (days: number) => renderConsecutiveDaysTag(days),
    },
    {
      title: '首次涨停',
      dataIndex: 'first_time',
      key: 'first_time',
      width: 100,
      render: (time: string) => (
        <Text style={{ color: colorScheme.onSurface, fontSize: '12px' }}>
          {time}
        </Text>
      ),
    },
    {
      title: '最后涨停',
      dataIndex: 'last_time',
      key: 'last_time',
      width: 100,
      render: (time: string) => (
        <Text style={{ color: colorScheme.onSurface, fontSize: '12px' }}>
          {time}
        </Text>
      ),
    },
    {
      title: '概念详情',
      dataIndex: 'concept_detail',
      key: 'concept_detail',
      width: 200,
      render: (detail: string) => (
        <Text 
          style={{ 
            color: colorScheme.onSurfaceVariant, 
            fontSize: '12px',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden'
          }}
          title={detail}
        >
          {detail}
        </Text>
      ),
    },
  ]

  // 计算统计数据
  const totalStocks = data.length
  const avgIncrease = data.length > 0 ? data.reduce((sum, item) => sum + item.increase_pct, 0) / data.length : 0
  const maxConsecutiveDays = data.length > 0 ? Math.max(...data.map(item => item.consecutive_days)) : 0
  const strongStocks = data.filter(item => item.consecutive_days >= 3).length

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '400px',
        backgroundColor: colorScheme.background
      }}>
        <Spin size="large" />
      </div>
    )
  }

  if (error) {
    return (
      <div style={{ padding: '24px', backgroundColor: colorScheme.background }}>
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <Button size="small" onClick={loadConceptStocks}>
              重试
            </Button>
          }
        />
      </div>
    )
  }

  return (
    <div style={{ backgroundColor: colorScheme.background, minHeight: '100vh' }}>
      {/* 面包屑导航 */}
      <Card 
        size="small" 
        style={{ 
          marginBottom: '16px',
          backgroundColor: colorScheme.surfaceContainerLow,
          borderColor: colorScheme.outlineVariant
        }}
      >
        <Space>
          <Breadcrumb
            items={[
              {
                href: '/',
                title: <HomeOutlined />,
              },
              {
                href: '/concept-analysis',
                title: (
                  <Space>
                    <BulbOutlined />
                    <span>概念分析</span>
                  </Space>
                ),
              },
              {
                title: `${conceptName}涨停股票`,
              },
            ]}
          />
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate(-1)}
            style={{ color: colorScheme.primary }}
          >
            返回
          </Button>
        </Space>
      </Card>

      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <Title 
          level={2} 
          style={{ 
            color: colorScheme.onSurface, 
            margin: 0,
            display: 'flex',
            alignItems: 'center',
            gap: '12px'
          }}
        >
          <TrophyOutlined style={{ color: colorScheme.primary }} />
          {conceptName}涨停股票
        </Title>
        <Text type="secondary" style={{ color: colorScheme.onSurfaceVariant }}>
          {conceptName}概念板块的涨停股票详细信息
        </Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card 
            size="small"
            style={{ 
              backgroundColor: colorScheme.surfaceContainerLow,
              borderColor: colorScheme.outlineVariant
            }}
          >
            <Statistic
              title="涨停股票数"
              value={totalStocks}
              valueStyle={{ color: colorScheme.primary }}
              prefix={<TrophyOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card 
            size="small"
            style={{ 
              backgroundColor: colorScheme.surfaceContainerLow,
              borderColor: colorScheme.outlineVariant
            }}
          >
            <Statistic
              title="平均涨幅"
              value={avgIncrease}
              precision={2}
              suffix="%"
              valueStyle={{ color: '#f5222d' }}
              prefix={<RiseOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card 
            size="small"
            style={{ 
              backgroundColor: colorScheme.surfaceContainerLow,
              borderColor: colorScheme.outlineVariant
            }}
          >
            <Statistic
              title="最高连板"
              value={maxConsecutiveDays}
              suffix="天"
              valueStyle={{ color: '#fa8c16' }}
              prefix={<ClockCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card 
            size="small"
            style={{ 
              backgroundColor: colorScheme.surfaceContainerLow,
              borderColor: colorScheme.outlineVariant
            }}
          >
            <Statistic
              title="强势股票"
              value={strongStocks}
              valueStyle={{ color: '#52c41a' }}
              suffix={`/ ${totalStocks}`}
            />
          </Card>
        </Col>
      </Row>

      {/* 数据表格 */}
      <Card
        title={
          <Space>
            <Text strong style={{ color: colorScheme.onSurface }}>
              涨停股票列表
            </Text>
            <Button
              type="text"
              icon={<ReloadOutlined />}
              onClick={loadConceptStocks}
              loading={loading}
              style={{ color: colorScheme.primary }}
            >
              刷新
            </Button>
          </Space>
        }
        style={{
          backgroundColor: colorScheme.surface,
          borderColor: colorScheme.outlineVariant
        }}
      >
        <Table
          columns={columns}
          dataSource={data}
          rowKey="stock_code"
          pagination={false}
          scroll={{ x: 1200, y: 600 }}
          size="small"
          style={{
            backgroundColor: colorScheme.surface
          }}
        />
      </Card>
    </div>
  )
}

export default ConceptStocks

import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom'
import {
  Card,
  Spin,
  Alert,
  Button,
  Tabs,
  Table,
  Row,
  Col,
  Typography,
  Tag,
  Breadcrumb,
  Space,
  App
} from 'antd'
import {
  ArrowLeftOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  TableOutlined,
  HomeOutlined,
  AppstoreOutlined,
  ReloadOutlined,
  ClockCircleOutlined
} from '@ant-design/icons'
import ReactECharts from 'echarts-for-react'
import { apiService } from '../../services/api'
import { useTheme } from '../../theme/ThemeProvider'

const { Title, Text } = Typography

// 数据接口定义
interface QuoteData {
  id: number
  quote_date: string
  open_price: number
  high_price: number
  low_price: number
  close_price: number
  volume: number
  price_change: number
  price_change_pct: number
}

interface AnalysisData {
  id: number
  analysis_date: string
  ma5: number
  ma10: number
  ma20: number
  ma60: number
  trend_direction: string
  trend_strength: number
  is_oscillating: boolean
  consecutive_up_days: number
  consecutive_down_days: number
  is_new_high_5d: boolean
  is_new_high_10d: boolean
  is_new_high_20d: boolean
  is_new_high_60d: boolean
  volatility: number
}

interface SectorData {
  id: number
  sector_code: string
  sector_name: string
  quotes: QuoteData[]
  analyses: AnalysisData[]
}

const AllSectorDetail: React.FC = () => {
  const { sectorCode } = useParams<{ sectorCode: string }>()
  const navigate = useNavigate()
  const { colorScheme } = useTheme()
  const { message } = App.useApp()

  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [sectorData, setSectorData] = useState<SectorData | null>(null)
  const [quotes, setQuotes] = useState<QuoteData[]>([])
  const [analyses, setAnalyses] = useState<AnalysisData[]>([])
  const [chartTimeframe, setChartTimeframe] = useState<'daily' | 'weekly' | 'monthly'>('daily')
  const [isUpdating, setIsUpdating] = useState(false)
  const [isUpdatingIndicators, setIsUpdatingIndicators] = useState(false)
  const [dataUpdateTime, setDataUpdateTime] = useState<string>('')

  // 加载板块详情数据
  const loadSectorDetail = async () => {
    if (!sectorCode) {
      setError('板块代码参数缺失')
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)
      
      const response = await apiService.getSectorDetail(sectorCode, 30, 7)
      
      if (response.data.success) {
        const data = response.data.data
        setSectorData(data)
        setQuotes(data.quotes || [])
        setAnalyses(data.analyses || [])

        // 设置数据更新时间
        if (response.data.timestamp) {
          try {
            const timestamp = new Date(response.data.timestamp)
            setDataUpdateTime(timestamp.toLocaleString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
              hour12: false
            }))
          } catch (e) {
            console.warn('时间戳格式转换失败:', response.data.timestamp)
          }
        }

        message.success(`成功加载板块 ${data.sector_name} 的详细数据`)
      } else {
        setError(response.data.error || '获取板块详情失败')
      }
    } catch (err: any) {
      console.error('获取板块详情失败:', err)
      setError(err.response?.data?.error || err.message || '获取板块详情失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadSectorDetail()
  }, [sectorCode])

  // 设置页面标题
  useEffect(() => {
    if (sectorData?.sector_name) {
      document.title = `板块详情 - ${sectorData.sector_name} | 股票分析系统`
    } else {
      document.title = '板块详情 | 股票分析系统'
    }

    // 组件卸载时恢复默认标题
    return () => {
      document.title = '股票分析系统'
    }
  }, [sectorData?.sector_name])

  // 更新单个板块数据
  const handleUpdateSingleSector = async () => {
    if (!sectorCode) return

    try {
      setIsUpdating(true)
      message.loading('正在更新板块数据...', 0)

      const response = await apiService.updateSingleSector(sectorCode)

      if (response.data.success) {
        message.destroy()
        message.success(`板块 ${sectorData?.sector_name} 数据更新完成`)

        // 重新加载数据
        await loadSectorDetail()
      } else {
        throw new Error(response.data.error || '更新失败')
      }
    } catch (err: any) {
      message.destroy()
      message.error('更新板块数据失败: ' + (err.response?.data?.error || err.message || '请稍后重试'))
    } finally {
      setIsUpdating(false)
    }
  }

  // 更新单个板块技术指标
  const handleUpdateSectorIndicators = async () => {
    if (!sectorCode) return

    try {
      setIsUpdatingIndicators(true)
      message.loading('正在重算技术指标...', 0)

      const response = await apiService.updateSingleSectorIndicators(sectorCode)

      if (response.data.success) {
        message.destroy()
        message.success(`板块 ${sectorData?.sector_name} 技术指标更新完成`)

        // 重新加载数据
        await loadSectorDetail()
      } else {
        throw new Error(response.data.error || '更新失败')
      }
    } catch (err: any) {
      message.destroy()
      message.error('更新技术指标失败: ' + (err.response?.data?.error || err.message || '请稍后重试'))
    } finally {
      setIsUpdatingIndicators(false)
    }
  }

  // 获取K线图配置
  const getKLineOption = () => {
    if (!quotes.length) return {}

    const sortedQuotes = [...quotes].sort((a, b) => 
      new Date(a.quote_date).getTime() - new Date(b.quote_date).getTime()
    )

    const data = sortedQuotes.map(q => [
      q.quote_date,
      q.open_price,
      q.close_price,
      q.low_price,
      q.high_price
    ])

    return {
      title: {
        text: `${sectorData?.sector_name || ''} K线图`,
        left: 'center',
        textStyle: {
          color: colorScheme.onSurface
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        backgroundColor: colorScheme.surfaceContainerHigh,
        borderColor: colorScheme.outline,
        textStyle: {
          color: colorScheme.onSurface
        }
      },
      xAxis: {
        type: 'category',
        data: sortedQuotes.map(q => q.quote_date),
        scale: true,
        boundaryGap: false,
        axisLine: { 
          onZero: false,
          lineStyle: { color: colorScheme.outline }
        },
        axisLabel: {
          color: colorScheme.onSurfaceVariant
        },
        splitLine: { show: false },
        splitNumber: 20,
        min: 'dataMin',
        max: 'dataMax'
      },
      yAxis: {
        scale: true,
        axisLine: {
          lineStyle: { color: colorScheme.outline }
        },
        axisLabel: {
          color: colorScheme.onSurfaceVariant
        },
        splitArea: {
          show: true,
          areaStyle: {
            color: [colorScheme.surfaceContainerLowest, 'transparent']
          }
        }
      },
      series: [
        {
          name: 'K线',
          type: 'candlestick',
          data: data.map(d => [d[1], d[2], d[3], d[4]]),
          itemStyle: {
            color: '#ef232a',      // 中文股市：红色表示上涨
            color0: '#14b143',     // 中文股市：绿色表示下跌
            borderColor: '#ef232a',
            borderColor0: '#14b143'
          }
        }
      ],
      backgroundColor: 'transparent'
    }
  }

  // 获取移动平均线图配置
  const getMaLineOption = () => {
    if (!analyses.length) return {}

    const sortedAnalyses = [...analyses].sort((a, b) =>
      new Date(a.analysis_date).getTime() - new Date(b.analysis_date).getTime()
    )

    const dates = sortedAnalyses.map(a => a.analysis_date)
    const ma5Data = sortedAnalyses.map(a => a.ma5)
    const ma10Data = sortedAnalyses.map(a => a.ma10)
    const ma20Data = sortedAnalyses.map(a => a.ma20)
    const ma60Data = sortedAnalyses.map(a => a.ma60)

    return {
      title: {
        text: '移动平均线',
        left: 'center',
        textStyle: {
          color: colorScheme.onSurface
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: colorScheme.surfaceContainerHigh,
        borderColor: colorScheme.outline,
        textStyle: {
          color: colorScheme.onSurface
        }
      },
      legend: {
        data: ['MA5', 'MA10', 'MA20', 'MA60'],
        top: 30,
        textStyle: {
          color: colorScheme.onSurface
        }
      },
      xAxis: {
        type: 'category',
        data: dates,
        axisLine: {
          lineStyle: { color: colorScheme.outline }
        },
        axisLabel: {
          color: colorScheme.onSurfaceVariant
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: { color: colorScheme.outline }
        },
        axisLabel: {
          color: colorScheme.onSurfaceVariant
        },
        splitLine: {
          lineStyle: { color: colorScheme.outlineVariant }
        }
      },
      series: [
        {
          name: 'MA5',
          type: 'line',
          data: ma5Data,
          smooth: true,
          lineStyle: { color: colorScheme.primary, width: 2 }
        },
        {
          name: 'MA10',
          type: 'line',
          data: ma10Data,
          smooth: true,
          lineStyle: { color: colorScheme.secondary, width: 2 }
        },
        {
          name: 'MA20',
          type: 'line',
          data: ma20Data,
          smooth: true,
          lineStyle: { color: colorScheme.tertiary, width: 2 }
        },
        {
          name: 'MA60',
          type: 'line',
          data: ma60Data,
          smooth: true,
          lineStyle: { color: colorScheme.warning, width: 2 }
        }
      ],
      backgroundColor: 'transparent'
    }
  }

  // 获取合并的K线图和移动平均线图配置
  const getCombinedChartOption = () => {
    if (!quotes.length || !analyses.length) return {}

    // 按日期升序排序K线数据
    const sortedQuotes = [...quotes].sort((a, b) =>
      new Date(a.quote_date).getTime() - new Date(b.quote_date).getTime()
    )

    // 按日期升序排序分析数据
    const sortedAnalyses = [...analyses].sort((a, b) =>
      new Date(a.analysis_date).getTime() - new Date(b.analysis_date).getTime()
    )

    // 准备K线数据
    const klineData = sortedQuotes.map(q => [
      q.quote_date,
      q.open_price,
      q.close_price,
      q.low_price,
      q.high_price
    ])





    // 准备移动平均线数据 - 需要与K线数据的日期对齐
    const dates = sortedQuotes.map(q => q.quote_date)
    const ma5Data = []
    const ma10Data = []
    const ma20Data = []
    const ma60Data = []

    // 为每个K线日期找到对应的移动平均线数据
    dates.forEach(date => {
      const analysis = sortedAnalyses.find(a => a.analysis_date === date)
      ma5Data.push(analysis ? analysis.ma5 : null)
      ma10Data.push(analysis ? analysis.ma10 : null)
      ma20Data.push(analysis ? analysis.ma20 : null)
      ma60Data.push(analysis ? analysis.ma60 : null)
    })

    return {
      title: {
        text: `K线图与移动平均线 (${chartTimeframe === 'daily' ? '日线' : chartTimeframe === 'weekly' ? '周线' : '月线'})`,
        left: 'center',
        textStyle: {
          color: colorScheme.onSurface,
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: colorScheme.surfaceContainerHigh,
        borderColor: colorScheme.outline,
        textStyle: {
          color: colorScheme.onSurface
        },
        formatter: function(params) {
          let result = `<div style="font-weight: bold; margin-bottom: 8px;">${params[0].axisValue}</div>`

          // K线数据
          const klineParam = params.find(p => p.seriesName === 'K线')
          if (klineParam && klineParam.data) {
            const [dataIndex, open, close, low, high] = klineParam.data // 正确格式: [dataIndex, open, close, low, high]

            const change = close - open
            const changePercent = ((change / open) * 100).toFixed(2)
            const changeColor = change >= 0 ? '#ef232a' : '#14b143'

            result += `<div style="margin-bottom: 8px;">
              <div>开盘: ${open?.toFixed(2)}</div>
              <div>收盘: ${close?.toFixed(2)}</div>
              <div>最高: ${high?.toFixed(2)}</div>
              <div>最低: ${low?.toFixed(2)}</div>
              <div style="color: ${changeColor};">涨跌: ${change >= 0 ? '+' : ''}${change?.toFixed(2)} (${changePercent}%)</div>
            </div>`
          }

          // 移动平均线数据
          const maParams = params.filter(p => p.seriesName.startsWith('MA'))
          if (maParams.length > 0) {
            result += '<div style="border-top: 1px solid #ccc; padding-top: 8px;">'
            maParams.forEach(param => {
              if (param.data !== null) {
                result += `<div style="color: ${param.color};">${param.seriesName}: ${param.data?.toFixed(2)}</div>`
              }
            })
            result += '</div>'
          }

          return result
        }
      },
      legend: {
        data: ['K线', 'MA5', 'MA10', 'MA20', 'MA60'],
        top: 35,
        textStyle: {
          color: colorScheme.onSurface
        }
      },
      xAxis: {
        type: 'category',
        data: dates,
        axisLine: {
          lineStyle: { color: colorScheme.outline }
        },
        axisLabel: {
          color: colorScheme.onSurfaceVariant,
          rotate: 45
        }
      },
      yAxis: {
        scale: true,
        axisLine: {
          lineStyle: { color: colorScheme.outline }
        },
        axisLabel: {
          color: colorScheme.onSurfaceVariant
        },
        splitArea: {
          show: true,
          areaStyle: {
            color: [colorScheme.surfaceContainerLowest, 'transparent']
          }
        },
        splitLine: {
          lineStyle: { color: colorScheme.outlineVariant }
        }
      },
      series: [
        {
          name: 'K线',
          type: 'candlestick',
          data: klineData.map(d => [d[1], d[2], d[3], d[4]]), // 正确格式: [open, close, low, high]
          itemStyle: {
            color: '#ef232a',      // 中文股市：红色表示上涨
            color0: '#14b143',     // 中文股市：绿色表示下跌
            borderColor: '#ef232a',
            borderColor0: '#14b143'
          },
          emphasis: {
            itemStyle: {
              borderWidth: 2
            }
          }
        },
        {
          name: 'MA5',
          type: 'line',
          data: ma5Data,
          smooth: true,
          lineStyle: {
            color: colorScheme.primary,
            width: 2
          },
          symbol: 'none',
          connectNulls: false
        },
        {
          name: 'MA10',
          type: 'line',
          data: ma10Data,
          smooth: true,
          lineStyle: {
            color: colorScheme.secondary,
            width: 2
          },
          symbol: 'none',
          connectNulls: false
        },
        {
          name: 'MA20',
          type: 'line',
          data: ma20Data,
          smooth: true,
          lineStyle: {
            color: colorScheme.tertiary,
            width: 2
          },
          symbol: 'none',
          connectNulls: false
        },
        {
          name: 'MA60',
          type: 'line',
          data: ma60Data,
          smooth: true,
          lineStyle: {
            color: colorScheme.warning,
            width: 2
          },
          symbol: 'none',
          connectNulls: false
        }
      ],
      backgroundColor: 'transparent',
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      }
    }
  }

  // 行情数据表格列定义
  const quoteColumns = [
    {
      title: '日期',
      dataIndex: 'quote_date',
      key: 'quote_date',
      width: 100,
    },
    {
      title: '开盘价',
      dataIndex: 'open_price',
      key: 'open_price',
      width: 80,
      render: (value: number) => value?.toFixed(2) || '-',
    },
    {
      title: '最高价',
      dataIndex: 'high_price',
      key: 'high_price',
      width: 80,
      render: (value: number) => value?.toFixed(2) || '-',
    },
    {
      title: '最低价',
      dataIndex: 'low_price',
      key: 'low_price',
      width: 80,
      render: (value: number) => value?.toFixed(2) || '-',
    },
    {
      title: '收盘价',
      dataIndex: 'close_price',
      key: 'close_price',
      width: 80,
      render: (value: number) => value?.toFixed(2) || '-',
    },
    {
      title: '涨跌额',
      dataIndex: 'price_change',
      key: 'price_change',
      width: 80,
      render: (value: number) => {
        const color = value > 0 ? '#ef232a' : value < 0 ? '#14b143' : colorScheme.onSurface
        return (
          <span style={{ color }}>
            {value > 0 ? '+' : ''}{value?.toFixed(2) || '-'}
          </span>
        )
      },
    },
    {
      title: '涨跌幅(%)',
      dataIndex: 'price_change_pct',
      key: 'price_change_pct',
      width: 100,
      render: (value: number) => {
        const color = value > 0 ? '#ef232a' : value < 0 ? '#14b143' : colorScheme.onSurface
        return (
          <span style={{ color }}>
            {value > 0 ? '+' : ''}{value?.toFixed(2) || '-'}%
          </span>
        )
      },
    },
  ]

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '100px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16, color: colorScheme.onSurface }}>
          <Title level={4}>加载板块详情中...</Title>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div style={{ padding: '50px' }}>
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <Button
              size="small"
              danger
              onClick={() => loadSectorDetail()}
            >
              重新加载
            </Button>
          }
        />
      </div>
    )
  }

  return (
    <div style={{ backgroundColor: colorScheme.background, minHeight: '100vh' }}>
      {/* 面包屑导航 */}
      <Card 
        size="small" 
        style={{ 
          marginBottom: 16,
          backgroundColor: colorScheme.surfaceContainerLow,
          borderColor: colorScheme.outlineVariant
        }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Breadcrumb
            items={[
              {
                href: '#',
                title: <HomeOutlined />,
                onClick: (e) => {
                  e.preventDefault()
                  navigate('/')
                }
              },
              {
                href: '#',
                title: (
                  <>
                    <AppstoreOutlined />
                    <span>全部86板块</span>
                  </>
                ),
                onClick: (e) => {
                  e.preventDefault()
                  navigate('/')
                }
              },
              {
                title: sectorData?.sector_name || '板块详情',
              },
            ]}
          />
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/')}
            className="md3-button-outlined"
            style={{
              borderColor: colorScheme.outline,
              color: colorScheme.primary
            }}
          >
            返回列表
          </Button>
        </div>
      </Card>

      {/* 板块基本信息 */}
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap', gap: '8px' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <Title level={3} style={{ margin: 0, color: colorScheme.onSurface }}>
                {sectorData?.sector_name}
              </Title>
              <Tag color="blue">{sectorData?.sector_code}</Tag>
            </div>
            {dataUpdateTime && (
              <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                <ClockCircleOutlined style={{
                  color: colorScheme.onSurfaceVariant,
                  fontSize: '12px'
                }} />
                <Text style={{
                  color: colorScheme.onSurfaceVariant,
                  fontSize: '12px',
                  fontFamily: 'monospace'
                }}>
                  数据更新时间：{dataUpdateTime}
                </Text>
              </div>
            )}
          </div>
        }
        extra={
          <Space>
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={handleUpdateSingleSector}
              loading={isUpdating}
              size="small"
              style={{
                backgroundColor: colorScheme.primary,
                borderColor: colorScheme.primary,
                color: colorScheme.onPrimary,
              }}
            >
              更新数据
            </Button>
            <Button
              icon={<BarChartOutlined />}
              onClick={handleUpdateSectorIndicators}
              loading={isUpdatingIndicators}
              size="small"
              style={{
                borderColor: colorScheme.outline,
                color: colorScheme.primary,
              }}
            >
              重算指标
            </Button>
          </Space>
        }
        size="small"
        style={{
          marginBottom: 16,
          backgroundColor: colorScheme.surfaceContainerLow,
          borderColor: colorScheme.outlineVariant
        }}
      >
        <Row gutter={16}>
          <Col span={6}>
            <Text type="secondary">板块代码：</Text>
            <Text strong style={{ color: colorScheme.onSurface }}>{sectorData?.sector_code}</Text>
          </Col>
          <Col span={6}>
            <Text type="secondary">历史数据：</Text>
            <Text strong style={{ color: colorScheme.onSurface }}>{quotes.length} 条</Text>
          </Col>
          <Col span={6}>
            <Text type="secondary">分析数据：</Text>
            <Text strong style={{ color: colorScheme.onSurface }}>{analyses.length} 条</Text>
          </Col>
          <Col span={6}>
            <Text type="secondary">数据范围：</Text>
            <Text strong style={{ color: colorScheme.onSurface }}>最近30个交易日</Text>
          </Col>
        </Row>
      </Card>



      {/* 图表和数据标签页 */}
      <Tabs
        defaultActiveKey="charts"
        items={[
          {
            key: 'charts',
            label: (
              <span style={{ color: colorScheme.primary }}>
                <BarChartOutlined />
                图表分析
              </span>
            ),
            children: (
              <div>
                {/* 时间周期选择器 */}
                <Card
                  size="small"
                  style={{
                    marginBottom: 16,
                    backgroundColor: colorScheme.surfaceContainerLow,
                    borderColor: colorScheme.outlineVariant
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                    <Text strong style={{ color: colorScheme.onSurface }}>图表周期：</Text>
                    <Button.Group>
                      <Button
                        type={chartTimeframe === 'daily' ? 'primary' : 'default'}
                        onClick={() => setChartTimeframe('daily')}
                        style={{
                          backgroundColor: chartTimeframe === 'daily' ? colorScheme.primary : 'transparent',
                          borderColor: colorScheme.outline,
                          color: chartTimeframe === 'daily' ? colorScheme.onPrimary : colorScheme.onSurface
                        }}
                      >
                        日线
                      </Button>
                      <Button
                        type={chartTimeframe === 'weekly' ? 'primary' : 'default'}
                        onClick={() => setChartTimeframe('weekly')}
                        style={{
                          backgroundColor: chartTimeframe === 'weekly' ? colorScheme.primary : 'transparent',
                          borderColor: colorScheme.outline,
                          color: chartTimeframe === 'weekly' ? colorScheme.onPrimary : colorScheme.onSurface
                        }}
                      >
                        周线
                      </Button>
                      <Button
                        type={chartTimeframe === 'monthly' ? 'primary' : 'default'}
                        onClick={() => setChartTimeframe('monthly')}
                        style={{
                          backgroundColor: chartTimeframe === 'monthly' ? colorScheme.primary : 'transparent',
                          borderColor: colorScheme.outline,
                          color: chartTimeframe === 'monthly' ? colorScheme.onPrimary : colorScheme.onSurface
                        }}
                      >
                        月线
                      </Button>
                    </Button.Group>
                    <Text type="secondary" style={{ marginLeft: 'auto' }}>
                      当前显示：{chartTimeframe === 'daily' ? '日线数据' : chartTimeframe === 'weekly' ? '周线数据' : '月线数据'}
                    </Text>
                  </div>
                </Card>

                <Row gutter={16}>
                  <Col span={24}>
                    <Card
                      title={`K线图与移动平均线 (${chartTimeframe === 'daily' ? '日线' : chartTimeframe === 'weekly' ? '周线' : '月线'})`}
                      size="small"
                      style={{
                        height: '500px',
                        backgroundColor: colorScheme.surfaceContainerLow,
                        borderColor: colorScheme.outlineVariant
                      }}
                    >
                      <ReactECharts
                        option={getCombinedChartOption()}
                        style={{ height: '420px', width: '100%' }}
                      />
                    </Card>
                  </Col>
                </Row>
              </div>
            ),
          },
          {
            key: 'quotes',
            label: (
              <span style={{ color: colorScheme.primary }}>
                <TableOutlined />
                数据详情
              </span>
            ),
            children: (
              <Row gutter={16}>
                <Col span={24}>
                  <Card
                    title="历史行情数据"
                    size="small"
                    style={{
                      backgroundColor: colorScheme.surfaceContainerLow,
                      borderColor: colorScheme.outlineVariant
                    }}
                  >
                    <Table
                      columns={quoteColumns}
                      dataSource={quotes}
                      rowKey="id"
                      size="small"
                      pagination={{
                        pageSize: 10,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total, range) =>
                          `第 ${range[0]}-${range[1]} 条，共 ${total} 条数据`,
                      }}
                      scroll={{ x: 800 }}
                      style={{
                        backgroundColor: 'transparent',
                      }}
                    />
                  </Card>
                </Col>
              </Row>
            ),
          },
          {
            key: 'analysis',
            label: (
              <span style={{ color: colorScheme.primary }}>
                <BarChartOutlined />
                技术分析
              </span>
            ),
            children: (
              <Row gutter={16}>
                {/* 技术指标计算说明 */}
                <Col span={24}>
                  <Card
                    title="技术指标计算说明"
                    size="small"
                    style={{
                      marginBottom: 16,
                      backgroundColor: colorScheme.surfaceContainerLow,
                      borderColor: colorScheme.outlineVariant
                    }}
                  >
                    <Row gutter={16}>
                      <Col span={6}>
                        <div style={{ padding: '12px', backgroundColor: colorScheme.surfaceContainerHighest, borderRadius: '8px' }}>
                          <Title level={5} style={{ margin: 0, color: colorScheme.primary }}>趋势判断</Title>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            基于MA5、MA10、MA20的排列关系：<br/>
                            • 上涨：MA5 &gt; MA10 &gt; MA20<br/>
                            • 下跌：MA5 &lt; MA10 &lt; MA20<br/>
                            • 震荡：其他情况
                          </Text>
                        </div>
                      </Col>
                      <Col span={6}>
                        <div style={{ padding: '12px', backgroundColor: colorScheme.surfaceContainerHighest, borderRadius: '8px' }}>
                          <Title level={5} style={{ margin: 0, color: colorScheme.primary }}>震荡判断</Title>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            基于价格波动幅度：<br/>
                            • 高震荡：日涨跌幅 &gt; 3%<br/>
                            • 中震荡：1% &lt; 涨跌幅 ≤ 3%<br/>
                            • 低震荡：涨跌幅 ≤ 1%
                          </Text>
                        </div>
                      </Col>
                      <Col span={6}>
                        <div style={{ padding: '12px', backgroundColor: colorScheme.surfaceContainerHighest, borderRadius: '8px' }}>
                          <Title level={5} style={{ margin: 0, color: colorScheme.primary }}>连续上涨判断</Title>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            统计连续收盘价上涨天数：<br/>
                            • 计算连续正涨跌幅天数<br/>
                            • 重置条件：出现下跌日<br/>
                            • 显示当前连续上涨天数
                          </Text>
                        </div>
                      </Col>
                      <Col span={6}>
                        <div style={{ padding: '12px', backgroundColor: colorScheme.surfaceContainerHighest, borderRadius: '8px' }}>
                          <Title level={5} style={{ margin: 0, color: colorScheme.primary }}>新高判断</Title>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            对比历史价格水平：<br/>
                            • 5日新高：当前价 &gt; 近5日最高价<br/>
                            • 20日新高：当前价 &gt; 近20日最高价<br/>
                            • 60日新高：当前价 &gt; 近60日最高价
                          </Text>
                        </div>
                      </Col>
                    </Row>
                  </Card>
                </Col>

                {/* 移动平均线数据表格 */}
                <Col span={24}>
                  <Card
                    title="移动平均线数据"
                    size="small"
                    style={{
                      backgroundColor: colorScheme.surfaceContainerLow,
                      borderColor: colorScheme.outlineVariant
                    }}
                  >
                    <Table
                      columns={[
                        {
                          title: '日期',
                          dataIndex: 'analysis_date',
                          key: 'analysis_date',
                          width: 100,
                        },
                        {
                          title: 'MA5',
                          dataIndex: 'ma5',
                          key: 'ma5',
                          width: 80,
                          render: (value: number) => value?.toFixed(2) || '-',
                        },
                        {
                          title: 'MA10',
                          dataIndex: 'ma10',
                          key: 'ma10',
                          width: 80,
                          render: (value: number) => value?.toFixed(2) || '-',
                        },
                        {
                          title: 'MA20',
                          dataIndex: 'ma20',
                          key: 'ma20',
                          width: 80,
                          render: (value: number) => value?.toFixed(2) || '-',
                        },
                        {
                          title: 'MA60',
                          dataIndex: 'ma60',
                          key: 'ma60',
                          width: 80,
                          render: (value: number) => value?.toFixed(2) || '-',
                        },
                        {
                          title: '趋势方向',
                          dataIndex: 'trend_direction',
                          key: 'trend_direction',
                          width: 100,
                          render: (value: string) => {
                            const color = value === '上升趋势' ? 'red' :
                                         value === '下降趋势' ? 'green' : 'default'
                            return <Tag color={color}>{value}</Tag>
                          },
                        },
                        {
                          title: '连续上涨',
                          dataIndex: 'consecutive_up_days',
                          key: 'consecutive_up_days',
                          width: 100,
                          render: (value: number) => <span>{value || 0}天</span>,
                        },
                        {
                          title: '5日新高',
                          dataIndex: 'is_new_high_5d',
                          key: 'is_new_high_5d',
                          width: 100,
                          render: (value: boolean) => <Tag color={value ? 'red' : 'default'}>{value ? '是' : '否'}</Tag>,
                        },
                      ]}
                      dataSource={analyses}
                      rowKey="id"
                      size="small"
                      pagination={{
                        pageSize: 10,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total, range) =>
                          `第 ${range[0]}-${range[1]} 条，共 ${total} 条数据`,
                      }}
                      scroll={{ x: 800 }}
                      style={{
                        backgroundColor: 'transparent',
                      }}
                    />
                  </Card>
                </Col>

                {/* 实时技术指标计算演示 */}
                <Col span={24}>
                  <Card
                    title="实时技术指标计算演示"
                    size="small"
                    style={{
                      marginTop: 16,
                      backgroundColor: colorScheme.surfaceContainerLow,
                      borderColor: colorScheme.outlineVariant
                    }}
                  >
                    {quotes.length > 0 && analyses.length > 0 ? (
                      <Row gutter={16}>
                        <Col span={12}>
                          <div style={{ padding: '16px', backgroundColor: colorScheme.surfaceContainerHighest, borderRadius: '8px' }}>
                            <Title level={5} style={{ color: colorScheme.primary }}>当前技术状态</Title>
                            {(() => {
                              const latestQuote = quotes[0]  // 数据库查询返回降序排列，最新数据在开头
                              const latestAnalysis = analyses[0]  // 数据库查询返回降序排列，最新数据在开头
                              const recentQuotes = quotes.slice(0, 5)  // 取前5条最新数据

                              // 计算连续上涨天数（数据已经是降序排列，从最新开始计算）
                              let consecutiveUpDays = 0
                              for (let i = 0; i < recentQuotes.length - 1; i++) {
                                if (recentQuotes[i].price_change > 0) {
                                  consecutiveUpDays++
                                } else {
                                  break
                                }
                              }

                              // 判断新高状态（使用收盘价进行比较，与后端逻辑保持一致）
                              const recent5DayHigh = Math.max(...recentQuotes.map(q => q.close_price))
                              const isNew5DayHigh = latestQuote.close_price >= recent5DayHigh

                              return (
                                <div>
                                  <p><strong>最新收盘价：</strong><span style={{ color: latestQuote.price_change > 0 ? '#ef232a' : '#14b143' }}>{latestQuote.close_price?.toFixed(2)}</span></p>
                                  <p><strong>涨跌幅：</strong><span style={{ color: latestQuote.price_change_pct > 0 ? '#ef232a' : '#14b143' }}>{latestQuote.price_change_pct > 0 ? '+' : ''}{latestQuote.price_change_pct?.toFixed(2)}%</span></p>
                                  <p><strong>趋势类型：</strong><Tag color={latestAnalysis.trend_direction === '上升趋势' ? 'red' : latestAnalysis.trend_direction === '下降趋势' ? 'green' : 'default'}>{latestAnalysis.trend_direction}</Tag></p>
                                  <p><strong>连续上涨：</strong><span style={{ color: (latestAnalysis.consecutive_up_days || 0) > 0 ? '#ef232a' : colorScheme.onSurface }}>{latestAnalysis.consecutive_up_days || 0}天</span></p>
                                  <p><strong>5日新高：</strong><Tag color={isNew5DayHigh ? 'red' : 'default'}>{isNew5DayHigh ? '是' : '否'}</Tag></p>
                                </div>
                              )
                            })()}
                          </div>
                        </Col>
                        <Col span={12}>
                          <div style={{ padding: '16px', backgroundColor: colorScheme.surfaceContainerHighest, borderRadius: '8px' }}>
                            <Title level={5} style={{ color: colorScheme.primary }}>移动平均线状态</Title>
                            {(() => {
                              const latestAnalysis = analyses[0]  // 数据库查询返回降序排列，最新数据在开头
                              if (!latestAnalysis) return <Text>暂无数据</Text>

                              const { ma5, ma10, ma20, ma60 } = latestAnalysis

                              return (
                                <div>
                                  <p><strong>MA5：</strong><span style={{ color: '#ff6b6b' }}>{ma5?.toFixed(2)}</span></p>
                                  <p><strong>MA10：</strong><span style={{ color: '#4ecdc4' }}>{ma10?.toFixed(2)}</span></p>
                                  <p><strong>MA20：</strong><span style={{ color: '#45b7d1' }}>{ma20?.toFixed(2)}</span></p>
                                  <p><strong>MA60：</strong><span style={{ color: '#f39c12' }}>{ma60?.toFixed(2)}</span></p>
                                  <p><strong>排列关系：</strong>
                                    {ma5 > ma10 && ma10 > ma20 ?
                                      <Tag color="red">多头排列</Tag> :
                                      ma5 < ma10 && ma10 < ma20 ?
                                      <Tag color="green">空头排列</Tag> :
                                      <Tag color="orange">震荡排列</Tag>
                                    }
                                  </p>
                                </div>
                              )
                            })()}
                          </div>
                        </Col>
                      </Row>
                    ) : (
                      <div style={{ textAlign: 'center', padding: '40px', color: colorScheme.onSurfaceVariant }}>
                        <Text>暂无足够数据进行技术指标计算演示</Text>
                      </div>
                    )}
                  </Card>
                </Col>
              </Row>
            ),
          },
        ]}
      />
    </div>
  )
}

export default AllSectorDetail
